# Migration Plan: Gulp → Vite + Turbo

## Overview
Migrating multi-theme WordPress setup from Gulp to Vite + Turbo for faster parallel builds, better caching, and modern tooling.
**IMPORTANT: Maintaining existing WordPress theme folder structure - no theme folders will be moved!**

## Current Setup Analysis
- **22 themes** with shared parent theme (phoenix)
- **Sequential processing** of themes (slow)
- **Assets**: SCSS, JS, images, fonts
- **Linting**: ESLint, Stylelint
- **Features**: Sourcemaps, minification, concatenation
- **Structure**: Each theme folder contains WordPress-required files (style.css, functions.php, etc.)

## Migration Plan

### Phase 1: Setup Foundation ✅
- [✅] 1.1 Install Turbo and initialize monorepo structure
- [✅] 1.2 Create root package.json with workspaces (updated for WordPress structure)
- [✅] 1.3 Install Vite and core dependencies
- [✅] 1.4 Create base Vite configuration template

### Phase 2: Configure Build System ✅
- [✅] 2.1 Create Vite config for parent theme (phoenix)
- [✅] 2.2 Setup SCSS processing with glob imports
- [✅] 2.3 Configure JavaScript processing (ESLint, Babel, minification)
- [✅] 2.4 Setup asset handling (images, fonts)
- [✅] 2.5 Create turbo.json for pipeline configuration

### Phase 3: Theme Migration ⏳
- [ ] 3.1 Create package.json for each theme
- [ ] 3.2 Generate Vite configs for all themes
- [ ] 3.3 Update build scripts and commands
- [ ] 3.4 Test parallel building

### Phase 4: Development Experience ⏳
- [ ] 4.1 Setup watch mode and dev server
- [ ] 4.2 Configure hot reloading
- [ ] 4.3 Setup linting integration
- [ ] 4.4 Create helper scripts

### Phase 5: Testing & Migration ⏳
- [ ] 5.1 Test build outputs match current Gulp outputs
- [ ] 5.2 Performance benchmarking
- [ ] 5.3 Update documentation
- [ ] 5.4 Create migration guide for team

---

## Progress Log

**Started:** August 22, 2025
**Status:** Starting Phase 3 - Child Theme Migration
**Next Task:** 3.1 Create package.json for each child theme
**Key Decision:** Maintaining WordPress theme folder structure - using direct theme folders as workspaces
**Phoenix Success:** ✅ Build time: 5.04s, All outputs match Gulp expectations, No temporary files created
**Outputs:** 9 CSS files + 3 JS files + image/font assets
**Tech Solution:** Virtual modules for admin script bundling (no file pollution)---

## Notes
- Maintain backward compatibility during transition
- Keep Gulp config as fallback until migration complete
- Focus on identical output structure to avoid WordPress integration issues
- **Asset warnings are expected**: Some CSS references to missing images (logo.png, hero backgrounds) or paths that exist only at runtime. These don't affect build success.
