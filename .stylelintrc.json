{"extends": "stylelint-config-standard-scss", "plugins": ["stylelint-order", "stylelint-scss"], "rules": {"selector-class-pattern": "^[a-z]([a-z0-9-]+)?(__([a-z0-9]+-?)+)?(--([a-z0-9]+-?)+){0,2}$", "length-zero-no-unit": true, "alpha-value-notation": "number", "color-function-notation": "modern", "custom-property-pattern": "^[a-z][a-z0-9-]*$", "max-nesting-depth": 10, "declaration-block-single-line-max-declarations": 1, "declaration-property-value-disallowed-list": {"/^border/": ["none"], "/^background/": ["none"], "list-style": ["none"]}, "selector-max-id": 0, "declaration-no-important": true, "property-no-vendor-prefix": true, "value-no-vendor-prefix": true, "declaration-block-no-redundant-longhand-properties": true, "shorthand-property-no-redundant-values": true, "rule-empty-line-before": "always-multi-line", "scss/dollar-variable-pattern": "^[a-z][a-zA-Z0-9]+$", "scss/selector-no-redundant-nesting-selector": true, "scss/at-mixin-pattern": "^[a-z][a-zA-Z0-9]+$", "scss/at-function-pattern": "^[a-z][a-zA-Z0-9]+$", "scss/percent-placeholder-pattern": "^[a-z][a-zA-Z0-9]+$", "scss/at-rule-no-unknown": true, "scss/operator-no-unspaced": true, "scss/dollar-variable-empty-line-before": ["always", {"except": ["first-nested", "after-comment", "after-dollar-variable"], "ignore": ["after-comment", "inside-single-line-block"]}], "scss/at-mixin-argumentless-call-parentheses": "never", "scss/load-no-partial-leading-underscore": null, "scss/at-import-partial-extension": null, "scss/at-import-no-partial-leading-underscore": null, "at-rule-empty-line-before": null, "scss/double-slash-comment-empty-line-before": null, "scss/double-slash-comment-whitespace-inside": null, "property-no-unknown": true, "custom-property-no-missing-var-function": true, "media-feature-range-notation": "prefix", "color-hex-length": "short", "color-named": "never", "font-family-name-quotes": "always-where-required", "function-url-quotes": "always", "declaration-block-no-duplicate-properties": true, "no-duplicate-selectors": true, "no-empty-source": true, "no-descending-specificity": true, "scss/no-global-function-names": true, "scss/comment-no-empty": true, "selector-max-compound-selectors": 3, "selector-max-specificity": "0,3,2", "scss/at-extend-no-missing-placeholder": true, "scss/at-if-no-null": true, "scss/no-duplicate-mixins": true, "scss/map-keys-quotes": "always", "unit-allowed-list": ["em", "rem", "s", "px", "%", "deg", "vh", "vw"], "number-max-precision": 4}}