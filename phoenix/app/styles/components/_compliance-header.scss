.compliance-header {
    background-color: $compliance-bg; // fallback
    height: $compliance-height;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;

    &__plus-18 {
        border-radius: 50%;
        height: 28px;
        width: 28px;
        @include get-typography('subcaption');

        background-color: $color-system-error;
        color: $color-font-light;
        display: flex;
        align-items: center;
        justify-content: center;

        @include from (md) {
            height: 36px;
            width: 36px;
            @include get-typography('label');
        }
    }

    &--agco {
        background-color: $header-background; // AGCO header is branded, ie. ComeOn Canada/Ontario
        height: $compliance-height;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 100;
        display: flex;
        align-items: center;

        &__wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        &__text {
            @include get-typography('subcaption');

            color: $header-color;
        }
    }

    &--dga {
        background-color: $header-background; // DGA header is branded, ie. ComeOn, GetLucky
        align-items: center;
        display: flex;
        height: $compliance-height;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 100;
        border-bottom: 1px solid $compliance-header-border;

        &__wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        &__text {
            @include get-typography('label-bold');

            color: $header-color;
        }
    }

    &--gga {
        background-color: $header-background; // GGA header is branded, ie. Sunmaker Germany
        height: $compliance-height;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 100;
        display: flex;
        align-items: center;

        &__wrapper {
            width: 100%;
            display: flex;
            @include gap($gutter);

            align-items: center;
        }

        &__element {

            &--first {
                @include from(md) {
                    min-width: 36px;
                }
            }

            &--timer {
                min-width: 66px;
            }

            &--full-width {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        &__icon {
            display: inline-block;
            background-color: $color-system-info;
            border-radius: 14px;
            padding: 5px;
            line-height: 0;

            svg {
                height: 18px;
                width: 18px;
            }

            path {
                fill: $color-font-light;
            }
        }

        &__timer {
            position: relative;
            padding: 6px 8px 6px 28px;
            background-color: $color-system-info;
            height: 28px;
            box-sizing: border-box;
            border-radius: 14px;
            overflow: hidden;
            color: $color-font-light;
            @include get-typography('caption');
        }

        &__timer &__icon {
            position: absolute;
            top: 0;
            left: 0;
        }
    }

    &--ksa {
        background-color: $header-background; // Nerherlands header is branded, ie. ComeOn, GetLucky
        height: $compliance-height;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 100;
        border-bottom: 1px solid $compliance-header-border;

        &__wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            gap: 24px;
            padding: 0;
        }

        &__element {
            color: $header-color;
            width: auto;
            line-height: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            @include to(xs) {
                width: 80px;
            }

            a {
                text-decoration: none;
                display: flex;
                align-items: center;
            }

            &__text {
                font-size: 11px;
                margin-left: 2px;
                font-weight: 700;
            }

            &--has-timer {
                display: flex;
                flex-direction: column-reverse;
            }

            svg {
                height: 18px;
                width: auto;
                fill: $header-color;
                margin-right: 2px;

                path,
                g {
                    fill: $header-color;
                }
            }
        }
    }

    &--sga {
        background-color: $header-background;
        border-bottom: 1px solid $compliance-header-border;

        &__wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        &__element {
            line-height: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            width: auto;
            max-height: 26px;
            margin-right: 10px;
            color: $header-color;

            &:last-child {
                margin-right: 0;
            }

            @include to(xs) {
                max-width: 80px;
            }

            &--plus {
                @include get-typography('subcaption');

                text-align: center;
                margin: 0;
                width: auto;
            }

            &--has-timer {
                display: flex;
                flex-direction: column-reverse;
            }

            &--spelpaus {
                margin-left: 8px;
            }

            &--spelgranser {
                margin: 0 11px 0 1px;
            }

            svg {
                background: $color-primary-white;
                border: 1px solid rgba($header-color, 0.2);
                border-radius: 14px;
                height: 26px;
                width: auto;

                @include to(xs) {
                    height: 18px;
                }
            }
        }
    }
}
