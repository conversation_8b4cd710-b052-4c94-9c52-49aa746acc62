{"name": "phoenix-theme", "version": "2.9.3", "description": "Phoenix parent theme build configuration", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite build --mode development", "watch": "vite build --watch", "clean": "rm -rf dist", "lint": "npm run lint:js && npm run lint:css", "lint:js": "eslint 'app/scripts/**/*.js' 'admin/scripts/**/*.js'", "lint:css": "stylelint 'app/styles/**/*.scss' 'admin/styles/**/*.scss'"}, "devDependencies": {"vite": "^5.4.11", "vite-plugin-sass-glob-import": "^5.0.0"}}