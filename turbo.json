{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["vite.config.base.js", "phoenix/app/styles/**", "phoenix/admin/styles/**", "phoenix/app/scripts/**", "phoenix/admin/scripts/**"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["app/**", "admin/**", "components/**", "services/**", "vite.config.js"], "outputs": ["dist/**"]}, "dev": {"dependsOn": ["^build"], "inputs": ["app/**", "admin/**", "components/**", "services/**", "vite.config.js"], "outputs": ["dist/**"], "cache": false}, "clean": {"cache": false}, "lint": {"outputs": []}, "watch": {"cache": false, "persistent": true}}}