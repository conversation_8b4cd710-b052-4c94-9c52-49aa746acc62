import { defineConfig } from 'vite';
import { resolve } from 'path';
import copy from 'rollup-plugin-copy';
import sassGlobImports from 'vite-plugin-sass-glob-import';
import fs from 'fs';

/**
 * Virtual module plugin to handle admin script bundles without creating files
 */
const virtualModules = (modules) => {
  const moduleMap = new Map(Object.entries(modules));

  return {
    name: 'virtual-modules',
    resolveId(id) {
      if (moduleMap.has(id)) {
        return id;
      }
    },
    load(id) {
      if (moduleMap.has(id)) {
        return moduleMap.get(id);
      }
    }
  };
};

/**
 * Base Vite configuration for WordPress themes
 * This template will be used for all theme-specific configs
 */
export function createThemeConfig(themeName) {
  const isParentTheme = themeName === 'phoenix';

  // Use absolute path to avoid issues with different working directories
  const themesRoot = '/Users/<USER>/dev/phoenix/wp-content/themes';
  const themeRoot = resolve(themesRoot, themeName);

  // Build input object for rollup
  const input = {};

  // Add individual SCSS files (each becomes separate CSS file)
  const scssFiles = [
    'page.scss',
    'campaign.scss',
    'blog.scss',
    'games.scss',
    'help-page.scss',
    'survey.scss',
    'responsible-gaming.scss',
    'ad-banner.scss'
  ];

  scssFiles.forEach(file => {
    const filePath = resolve(themeRoot, `app/styles/${file}`);
    if (fs.existsSync(filePath)) {
      const name = file.replace('.scss', '');
      input[name] = filePath;
    }
  });

  // Add admin styles (becomes admin.min.css)
  const adminStylePath = resolve(themeRoot, `admin/styles/main.scss`);
  if (fs.existsSync(adminStylePath)) {
    input['admin-styles.min'] = adminStylePath;
  }

  // Virtual modules for admin scripts (no file creation)
  const virtualModuleContents = {};

  // Add JavaScript files ONLY for parent theme (phoenix)
  if (isParentTheme) {
    // Main frontend JS bundle
    const mainJsPath = resolve(themeRoot, `app/scripts/main.js`);
    if (fs.existsSync(mainJsPath)) {
      input['main'] = mainJsPath;
    }

    // Admin global scripts - bundle all global/**/*.js files
    const adminGlobalDir = resolve(themeRoot, `admin/scripts/global`);
    if (fs.existsSync(adminGlobalDir)) {
      // Find all JS files in global directory recursively
      const findJsFiles = (dir) => {
        let files = [];
        const items = fs.readdirSync(dir);
        for (const item of items) {
          const fullPath = resolve(dir, item);
          if (fs.statSync(fullPath).isDirectory()) {
            files = files.concat(findJsFiles(fullPath));
          } else if (item.endsWith('.js')) {
            files.push(fullPath);
          }
        }
        return files;
      };

      const globalJsFiles = findJsFiles(adminGlobalDir);

      if (globalJsFiles.length > 0) {
        // Create virtual module content for admin global scripts
        const adminGlobalEntry = globalJsFiles.map(filePath => {
          const relativePath = filePath.replace(themeRoot + '/', './');
          return `import '${relativePath}';`;
        }).join('\n');

        const virtualAdminId = 'virtual:admin-global';
        virtualModuleContents[virtualAdminId] = adminGlobalEntry;
        input['admin.min'] = virtualAdminId;
      }
    }

    // Admin post scripts - bundle all post/**/*.js files
    const postScriptsDir = resolve(themeRoot, `admin/scripts/post`);
    if (fs.existsSync(postScriptsDir)) {
      const postFiles = fs.readdirSync(postScriptsDir).filter(file => file.endsWith('.js'));
      if (postFiles.length > 0) {
        // Create virtual module content for post scripts
        const postEntry = postFiles.map(file =>
          `import './admin/scripts/post/${file}';`
        ).join('\n');

        const virtualPostId = 'virtual:admin-post';
        virtualModuleContents[virtualPostId] = postEntry;
        input['admin-post.min'] = virtualPostId;
      }
    }
  }

  console.log(`Building ${themeName} with inputs:`, Object.keys(input));

  return defineConfig({
    root: themeRoot,
    base: './',

    build: {
      outDir: 'dist',
      emptyOutDir: true,
      sourcemap: process.env.NODE_ENV === 'development',

      rollupOptions: {
        input,

        output: {
          assetFileNames: (assetInfo) => {
            if (/\.(css)$/.test(assetInfo.name)) {
              // Map CSS entry names to match Gulp output
              if (assetInfo.name.includes('admin-styles')) {
                return 'admin.min.css';
              }
              return `[name].css`;
            }
            return `assets/[name].[ext]`;
          },
          chunkFileNames: '[name].js',
          entryFileNames: '[name].js'
        }
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          // Make common imports available to all SCSS files
          // Only include essential imports that exist in all themes
          additionalData: themeName === 'phoenix' ? `
            @import "${resolve(process.cwd(), '..', 'phoenix/app/styles/brand/_variables.scss')}";
            @import "${resolve(process.cwd(), '..', 'phoenix/app/styles/mixins/_breakpoints.scss')}";
            @import "${resolve(process.cwd(), '..', 'phoenix/app/styles/mixins/_tools.scss')}";
          ` : `
            @import "${resolve(process.cwd(), '..', 'phoenix/app/styles/brand/_variables.scss')}";
            @import "${resolve(process.cwd(), '..', 'phoenix/app/styles/mixins/_breakpoints.scss')}";
            @import "${resolve(process.cwd(), '..', 'phoenix/app/styles/mixins/_tools.scss')}";
          `
        }
      }
    },

    plugins: [
      // Handle virtual modules for admin scripts (no file creation)
      virtualModules(virtualModuleContents),

      // Enable SCSS glob imports like @import 'blocks/**/*.scss'
      sassGlobImports(),

      // Copy images and handle fonts conditionally
      copy({
        targets: [
          {
            src: `app/images/**/*`,
            dest: `dist/images`,
            flatten: false
          },
          // Copy admin icons ONLY for phoenix theme
          ...(isParentTheme && fs.existsSync(resolve(themeRoot, `admin/icons`)) ? [{
            src: `admin/icons/**/*`,
            dest: `dist/admin/icons`,
            flatten: false
          }] : []),
          // Copy fonts if folder exists
          ...(fs.existsSync(resolve(themeRoot, `app/fonts`)) ? [{
            src: `app/fonts/**/*`,
            dest: `dist/fonts`,
            flatten: false
          }] : [])
        ],
        hook: 'writeBundle'
      })
    ],

    resolve: {
      alias: {
        '@': themeRoot,
        '@phoenix': resolve(process.cwd(), '..', 'phoenix'),
        '@styles': resolve(themeRoot, `app/styles`),
        '@scripts': resolve(themeRoot, `app/scripts`),
        '@components': resolve(themeRoot, `components`),
        // Fix asset path resolution
        '/wp-content/themes/phoenix': themeRoot
      }
    },

    // Handle asset imports and references
    assetsInclude: ['**/*.svg', '**/*.png', '**/*.jpg', '**/*.jpeg'],
  });
}

export default createThemeConfig;
