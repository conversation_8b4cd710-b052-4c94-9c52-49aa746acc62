{"name": "phoenix-themes-monorepo", "version": "2.9.3", "description": "Multi-theme WordPress build system with Vite + Turbo", "private": true, "workspaces": ["888", "casinostugan", "casinostuen", "cherrycasino", "comeon", "euroslots", "folkeriket", "<PERSON><PERSON><PERSON>", "getlucky", "hajper", "lyllo", "mobilautomaten", "mobilebet", "<PERSON><PERSON><PERSON><PERSON>", "norgesspill", "phoenix", "pzbuk", "snabbare", "spinon", "sunmaker", "<PERSON><PERSON><PERSON><PERSON>", "we<PERSON>in"], "scripts": {"build": "turbo run build", "build:legacy": "gulp", "dev": "turbo run dev", "dev:legacy": "gulp dev", "watch": "turbo run watch", "clean": "turbo run clean", "lint": "turbo run lint"}, "author": "Phoenix Team - ComeOn!", "license": "ISC", "repository": {"type": "git", "url": "https://bitbucket.org/comeonwordpress/phoenix/", "directory": "/"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.25.3", "@ronilaukkarinen/gulp-stylelint": "14.1.2", "@vitejs/plugin-legacy": "^7.2.1", "del": "^7.1.0", "eslint": "^8.49.0", "fast-glob": "^3.3.3", "glob": "^11.0.3", "gulp": "^5.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-dart-sass": "^1.1.0", "gulp-download": "^0.0.1", "gulp-eslint": "^6.0.0", "gulp-filter": "^9.0.1", "gulp-imagemin": "^9.1.0", "gulp-sass-glob": "^1.1.0", "gulp-size": "^5.0.0", "gulp-touch-cmd": "^0.0.1", "gulp-uglify": "^3.0.2", "gulp-watch": "^5.0.1", "gulplog": "^2.2.0", "lightningcss": "^1.30.1", "rollup-plugin-copy": "^3.5.0", "sass": "^1.77.6", "sass-glob-importer": "^1.0.1", "stylelint": "15.11.0", "stylelint-config-standard-scss": "11.1.0", "stylelint-order": "6.0.3", "stylelint-scss": "5.3.1", "turbo": "^2.5.6", "vite": "^7.1.3", "vite-plugin-eslint": "^1.8.1"}}